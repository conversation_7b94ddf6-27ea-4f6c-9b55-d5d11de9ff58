<?php

// Simple database check using PDO
try {
    $db = new PDO('mysql:host=localhost;dbname=simrps', 'root', '');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Database Connection Test ===" . PHP_EOL;
    echo "Database: simrps" . PHP_EOL;
    echo "Host: localhost" . PHP_EOL;
    echo PHP_EOL;

    echo "=== Tables in Database ===" . PHP_EOL;
    $result = $db->query("SHOW TABLES");
    $tables = [];
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
        echo "- " . $row[0] . PHP_EOL;
    }
    echo PHP_EOL;

    echo "=== Roles Table Data ===" . PHP_EOL;
    if (in_array('roles', $tables)) {
        $result = $db->query("SELECT * FROM roles");
        while ($role = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "ID: {$role['id']}, Name: {$role['name']}, Description: {$role['description']}" . PHP_EOL;
        }
    } else {
        echo "Roles table not found!" . PHP_EOL;
    }
    echo PHP_EOL;

    echo "=== Users Table Data ===" . PHP_EOL;
    if (in_array('users', $tables)) {
        $result = $db->query("SELECT * FROM users");
        $users = $result->fetchAll(PDO::FETCH_ASSOC);
        echo "Total users: " . count($users) . PHP_EOL;
        foreach ($users as $user) {
            echo "ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}" . PHP_EOL;
        }
    } else {
        echo "Users table not found!" . PHP_EOL;
    }
    echo PHP_EOL;

    echo "=== Migration History ===" . PHP_EOL;
    if (in_array('migrations', $tables)) {
        $result = $db->query("SELECT * FROM migrations ORDER BY id");
        while ($migration = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Version: {$migration['version']}, Class: {$migration['class']}, Group: {$migration['group']}, Namespace: {$migration['namespace']}" . PHP_EOL;
        }
    } else {
        echo "Migrations table not found!" . PHP_EOL;
    }

    echo PHP_EOL;
    echo "=== Faculties Table Data ===" . PHP_EOL;
    if (in_array('faculties', $tables)) {
        $result = $db->query("SELECT * FROM faculties");
        while ($faculty = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "ID: {$faculty['id']}, Code: {$faculty['code']}, Name: {$faculty['name']}" . PHP_EOL;
        }
    } else {
        echo "Faculties table not found!" . PHP_EOL;
    }

    echo PHP_EOL;
    echo "=== Study Programs Table Data ===" . PHP_EOL;
    if (in_array('study_programs', $tables)) {
        $result = $db->query("SELECT * FROM study_programs LIMIT 5");
        while ($sp = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "ID: {$sp['id']}, Code: {$sp['code']}, Name: {$sp['name']}, Faculty: {$sp['faculty_id']}" . PHP_EOL;
        }
    } else {
        echo "Study programs table not found!" . PHP_EOL;
    }

    echo PHP_EOL;
    echo "=== Assessment Methods Table Data ===" . PHP_EOL;
    if (in_array('assessment_methods', $tables)) {
        $result = $db->query("SELECT COUNT(*) as count FROM assessment_methods");
        $count = $result->fetch(PDO::FETCH_ASSOC);
        echo "Total assessment methods: {$count['count']}" . PHP_EOL;
    } else {
        echo "Assessment methods table not found!" . PHP_EOL;
    }

    echo PHP_EOL;
    echo "=== Database Summary ===" . PHP_EOL;
    echo "Total tables created: " . count($tables) . PHP_EOL;
    echo "Tables with data:" . PHP_EOL;
    foreach ($tables as $table) {
        if (strpos($table, 'v_') === 0) continue; // Skip views
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM `{$table}`");
            $count = $result->fetch(PDO::FETCH_ASSOC);
            echo "- {$table}: {$count['count']} records" . PHP_EOL;
        } catch (Exception $e) {
            echo "- {$table}: Error counting records" . PHP_EOL;
        }
    }

} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . PHP_EOL;
}
